import com.autoai.car.buildsrc.Versions
import com.autoai.car.buildsrc.Libs

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'org.jetbrains.kotlin.plugin.parcelize'
android {
    compileSdk Versions.COMPILE_SDK
    defaultConfig {
        minSdk Versions.MIN_SDK
        targetSdk Versions.TARGET_SDK
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs = ["-Xsam-conversions=class"]
    }

    // 使用 JVM toolchain 确保版本一致性
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlin {
        jvmToolchain(11)
    }
    viewBinding {
        enabled = true
    }
    lintOptions {
        abortOnError false
    }
    // Robolectric 测试框架需要
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }

}
//aspectjx {
//    // 排除一些第三方库的包名（Gson、 LeakCanary 和 AOP 有冲突）
//    // 否则就会起冲突：ClassNotFoundException: Didn't find class on path: DexPathList
//    exclude 'androidx'
//    exclude 'android.arch'
//    exclude 'android.support'
//    exclude 'com.google'
//    exclude 'versions.9'
//    exclude 'com.squareup'
//    exclude 'leakcanary'
//    exclude 'com.taobao'
//    exclude 'com.ut'
//    exclude 'kotlinx'
//    exclude 'com.tencent'
//    exclude 'tv.danmaku.ijk'
//}

dependencies {

    // kotlin
    implementation Libs.kotlin
    // material
    implementation Libs.material

    // Androidx
    implementation Libs.Androidx.coreKtx
    implementation Libs.Androidx.appcompat
    implementation Libs.Androidx.constraintlayout
    implementation Libs.Androidx.MultiDex
    implementation Libs.Androidx.ActivityKtx
    implementation Libs.Androidx.FragmentKtx

    // okhttp & retrofit
    implementation Libs.Retrofit.Okhttp3
    implementation Libs.Retrofit.Okhttp3Logging
    implementation Libs.Retrofit.Retrofit
    implementation Libs.Retrofit.RetrofitConverterGson

    // LifeCycle
    implementation Libs.LifeCycle.LifeCycle
    implementation Libs.LifeCycle.LifeCycleRuntime
    implementation Libs.LifeCycle.LifeCycleLivedataKtx
    implementation Libs.LifeCycle.LifeCycleViewmodelKtx
    implementation Libs.LifeCycle.LifeCycleProcess

    // coroutines
    implementation Libs.Coroutines.Coroutines
    implementation Libs.Coroutines.CoroutinesAndroid

    // Room
    implementation Libs.Room.roomRuntime
    implementation Libs.Room.roomKtx
    kapt Libs.Room.roomCompiler

    // ThirdLibs
    implementation Libs.ThirdLibs.Gson
    implementation Libs.ThirdLibs.MMKV

}