package com.autoai.car.buildsrc

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 依赖库版本管理
 */
object Libs {
    object ProjectPluginManager {
        const val buildGradle = "com.android.tools.build:gradle:8.1.1"
//        const val buildGradle = "com.android.tools.build:gradle:7.1.0"
        const val kotlinGradlePlugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.10"

        const val HiltPlugin = "com.google.dagger:hilt-android-gradle-plugin:2.38.1"
        const val AspectjxPlugin = "io.github.wurensen:gradle-android-plugin-aspectjx:3.3.2"
    }

    const val kotlin = "org.jetbrains.kotlin:kotlin-stdlib:1.8.10"
    const val material = "com.google.android.material:material:1.10.0"

    object Androidx {
        const val coreKtx = "androidx.core:core-ktx:1.12.0"
        const val appcompat = "androidx.appcompat:appcompat:1.6.1"
        const val constraintlayout = "androidx.constraintlayout:constraintlayout:2.1.4"
        const val MultiDex = "androidx.multidex:multidex:2.0.1"
        const val ActivityKtx = "androidx.activity:activity-ktx:1.8.2"
        const val FragmentKtx = "androidx.fragment:fragment-ktx:1.6.2"
    }

    object Retrofit {
        // 降级到与 Kotlin 1.8.10 兼容的版本
        const val Okhttp3 = "com.squareup.okhttp3:okhttp:4.12.0"
        const val Okhttp3Logging = "com.squareup.okhttp3:logging-interceptor:4.12.0"

        const val Retrofit = "com.squareup.retrofit2:retrofit:2.9.0"
        const val RetrofitConverterGson = "com.squareup.retrofit2:converter-gson:2.9.0"
    }

    //lifecycle
    object LifeCycle {
        private const val lifecycle_version = "2.7.0"

        const val LifeCycle = "androidx.lifecycle:lifecycle-extensions:2.2.0"
        const val LifeCycleRuntime = "androidx.lifecycle:lifecycle-runtime:$lifecycle_version"
        const val LifeCycleLivedataKtx =
            "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
        const val LifeCycleViewmodelKtx =
            "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
        const val LifeCycleProcess =
            "androidx.lifecycle:lifecycle-process:$lifecycle_version"
    }

    //kotlin coroutines
    object Coroutines {
        private const val coroutines_version = "1.7.3"
        const val Coroutines = "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines_version"
        const val CoroutinesAndroid =
            "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_version"
    }

    // Room数据库
    object Room {
        private const val version = "2.5.0"
        // 数据库
        const val roomRuntime = "androidx.room:room-runtime:$version"
        const val roomKtx = "androidx.room:room-ktx:$version"
        const val roomCompiler = "androidx.room:room-compiler:$version"
    }

    object ThirdLibs {
        const val MMKV = "com.tencent:mmkv:2.2.3"
        const val Gson = "com.google.code.gson:gson:2.13.1"
    }
}

//版本相关
object Versions {
    //编译版本
    const val COMPILE_SDK = 36
    //最小版本
    const val MIN_SDK = 31
    // 目标版本
    const val TARGET_SDK = 36
    //versionCode
    const val VERSION_CODE = 202508261
    // versionName
    const val VERSION_NAME = "1.0.0"
   // Application id
    const val APPLICATION_ID = "com.autoai.aiosservice"
}