#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1390576 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=27000, tid=540
#
# JRE version: OpenJDK Runtime Environment Corretto-**********.1 (17.0.13+11) (build 17.0.13+11-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-**********.1 (17.0.13+11-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC -XX:G1HeapRegionSize=32m -XX:+UseStringDeduplication --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.desktop/java.awt.font=ALL-UNNAMED -Xmx8192m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13

Host: 13th Gen Intel(R) Core(TM) i7-13700H, 20 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
Time: Wed Aug 27 12:51:23 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4768) elapsed time: 103.976339 seconds (0d 0h 1m 43s)

---------------  T H R E A D  ---------------

Current thread (0x000001674567c7a0):  JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=540, stack(0x00000011de000000,0x00000011de100000)]


Current CompileTask:
C2: 103976 22126       4       com.android.tools.r8.graph.H5::a (32 bytes)

Stack: [0x00000011de000000,0x00000011de100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x680c19]
V  [jvm.dll+0x83888a]
V  [jvm.dll+0x83a34e]
V  [jvm.dll+0x83a9b3]
V  [jvm.dll+0x2481af]
V  [jvm.dll+0xac9f4]
V  [jvm.dll+0xad03c]
V  [jvm.dll+0x2af7df]
V  [jvm.dll+0x587047]
V  [jvm.dll+0x223012]
V  [jvm.dll+0x22340f]
V  [jvm.dll+0x21c530]
V  [jvm.dll+0x219a31]
V  [jvm.dll+0x1a58ed]
V  [jvm.dll+0x22988d]
V  [jvm.dll+0x227a1c]
V  [jvm.dll+0x7ed737]
V  [jvm.dll+0x7e7b2c]
V  [jvm.dll+0x67fae7]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016745d44e90, length=174, elements={
0x00000167582801a0, 0x000001677d2b1f50, 0x000001677d2b2630, 0x000001677d2cd940,
0x000001677da5c230, 0x000001677da5fe80, 0x000001677d2d2970, 0x000001677da61bf0,
0x000001677da874e0, 0x000001677d2de280, 0x000001677dbbcaa0, 0x000001677dcc89a0,
0x000001677e75e2c0, 0x000001677f5f5de0, 0x000001677e260020, 0x000001677dcb3040,
0x000001677f610740, 0x000001677f60fd20, 0x000001677f60dec0, 0x000001677f611160,
0x0000016741b50810, 0x0000016741b4eec0, 0x0000016741b4da80, 0x0000016741b4f3d0,
0x0000016741b53090, 0x0000016741b544d0, 0x0000016741d24f90, 0x0000016741d2afc0,
0x0000016741d2a090, 0x0000016741d2a5a0, 0x0000016744bbebf0, 0x0000016744bbf610,
0x0000016741b4d060, 0x00000167455d17e0, 0x00000167455ce540, 0x00000167455d08b0,
0x00000167455cf470, 0x00000167455d5ec0, 0x00000167455d2c20, 0x00000167455d3b50,
0x00000167455d4570, 0x00000167455d59b0, 0x00000167455d3130, 0x00000167455d54a0,
0x0000016745ed2890, 0x0000016745ed0520, 0x0000016745ed2380, 0x0000016745ed1e70,
0x0000016745ecf0e0, 0x0000016745ed0f40, 0x0000016745ed0010, 0x0000016745ecf5f0,
0x0000016745ed1450, 0x0000016745ed1960, 0x0000016741d24a80, 0x0000016741d25ec0,
0x0000016741d254a0, 0x0000016741d259b0, 0x0000016741d263d0, 0x0000016741d24060,
0x0000016741d28230, 0x0000016741d28740, 0x0000016741d29670, 0x0000016741d28c50,
0x0000016741d2aab0, 0x0000016741d29160, 0x0000016741d29b80, 0x0000016741d27300,
0x0000016741d27810, 0x0000016741d27d20, 0x0000016741d2b9e0, 0x0000016741b51740,
0x0000016741b4df90, 0x0000016741b4e9b0, 0x0000016741b52670, 0x0000016741b52b80,
0x0000016741b535a0, 0x0000016741b4fdf0, 0x0000016741b53ab0, 0x0000016741b50300,
0x0000016741fe6140, 0x0000016741fe33b0, 0x0000016741fe6650, 0x0000016741fe38c0,
0x0000016741fe6b60, 0x0000016741fe42e0, 0x0000016741fe47f0, 0x0000016744bbdcc0,
0x0000016744bbf100, 0x0000016744bc0030, 0x0000016744bc0f60, 0x0000016744bc0540,
0x0000016744bc0a50, 0x0000016744bbd7b0, 0x0000016744bbe1d0, 0x000001677f60edf0,
0x000001677f60f300, 0x000001677f60f810, 0x000001677f610230, 0x000001677f610c50,
0x00000167455d4a80, 0x0000016747d7dcc0, 0x0000016747d7e6e0, 0x0000016747d7f100,
0x0000016747d7f610, 0x0000016747d7d7b0, 0x0000016747d7c880, 0x0000016747d7cd90,
0x0000016741ca91b0, 0x0000016741ca7860, 0x0000016741ca8790, 0x0000016741cab520,
0x0000016741ca8ca0, 0x0000016741ca96c0, 0x0000016741ca9bd0, 0x0000016741ca7d70,
0x0000016741ca8280, 0x0000016741caa0e0, 0x0000016741cadda0, 0x0000016741cae2b0,
0x0000016741caab00, 0x0000016741cac450, 0x0000016741caba30, 0x0000016741cac960,
0x0000016741cace70, 0x0000016741cae7c0, 0x0000016741caecd0, 0x0000016741cabf40,
0x0000016741caf1e0, 0x0000016741cad380, 0x00000167437d3730, 0x00000167437d4660,
0x00000167437d0eb0, 0x00000167437d2d10, 0x00000167437d4b70, 0x00000167437d3220,
0x00000167437d5080, 0x00000167437d5aa0, 0x00000167437d5590, 0x00000167437d3c40,
0x00000167437d5fb0, 0x00000167437d2800, 0x00000167437d6ee0, 0x00000167437d73f0,
0x00000167456772a0, 0x0000016745678d30, 0x00000167456787e0, 0x0000016745675d60,
0x0000016745675810, 0x0000016745679d20, 0x0000016745676800, 0x000001674567c7a0,
0x000001674567a7c0, 0x000001674567b260, 0x00000167437d7e10, 0x00000167437d7900,
0x00000167437d8320, 0x00000167455d03a0, 0x0000016744f369d0, 0x0000016744f33730,
0x0000016744f35fb0, 0x0000016744f33c40, 0x0000016744f33220, 0x0000016744f364c0,
0x0000016744f36ee0, 0x0000016744f373f0, 0x0000016744f37900, 0x0000016744f35590,
0x0000016744f34150, 0x0000016744f37e10, 0x0000016744f34660, 0x0000016744f38320,
0x0000016744f34b70, 0x0000016744f35080
}

Java Threads: ( => current thread )
  0x00000167582801a0 JavaThread "main" [_thread_blocked, id=22996, stack(0x00000011d2f00000,0x00000011d3000000)]
  0x000001677d2b1f50 JavaThread "Reference Handler" daemon [_thread_blocked, id=26512, stack(0x00000011d3700000,0x00000011d3800000)]
  0x000001677d2b2630 JavaThread "Finalizer" daemon [_thread_blocked, id=43824, stack(0x00000011d3800000,0x00000011d3900000)]
  0x000001677d2cd940 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=2768, stack(0x00000011d3900000,0x00000011d3a00000)]
  0x000001677da5c230 JavaThread "Attach Listener" daemon [_thread_blocked, id=18928, stack(0x00000011d3a00000,0x00000011d3b00000)]
  0x000001677da5fe80 JavaThread "Service Thread" daemon [_thread_blocked, id=22184, stack(0x00000011d3b00000,0x00000011d3c00000)]
  0x000001677d2d2970 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=24972, stack(0x00000011d3c00000,0x00000011d3d00000)]
  0x000001677da61bf0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=32008, stack(0x00000011d3d00000,0x00000011d3e00000)]
  0x000001677da874e0 JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=26248, stack(0x00000011d3e00000,0x00000011d3f00000)]
  0x000001677d2de280 JavaThread "Sweeper thread" daemon [_thread_blocked, id=10388, stack(0x00000011d3f00000,0x00000011d4000000)]
  0x000001677dbbcaa0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=32748, stack(0x00000011d4100000,0x00000011d4200000)]
  0x000001677dcc89a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=520, stack(0x00000011d4400000,0x00000011d4500000)]
  0x000001677e75e2c0 JavaThread "Daemon health stats" [_thread_blocked, id=29940, stack(0x00000011d5500000,0x00000011d5600000)]
  0x000001677f5f5de0 JavaThread "Incoming local TCP Connector on port 8433" [_thread_in_native, id=21592, stack(0x00000011d5400000,0x00000011d5500000)]
  0x000001677e260020 JavaThread "Daemon periodic checks" [_thread_blocked, id=13820, stack(0x00000011d5600000,0x00000011d5700000)]
  0x000001677dcb3040 JavaThread "Daemon" [_thread_blocked, id=26292, stack(0x00000011d5700000,0x00000011d5800000)]
  0x000001677f610740 JavaThread "Daemon worker" [_thread_blocked, id=2812, stack(0x00000011d4800000,0x00000011d4900000)]
  0x000001677f60fd20 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=34128, stack(0x00000011d5d00000,0x00000011d5e00000)]
  0x000001677f60dec0 JavaThread "File lock request listener" [_thread_in_native, id=44444, stack(0x00000011d5e00000,0x00000011d5f00000)]
  0x000001677f611160 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.13\fileHashes)" [_thread_blocked, id=37136, stack(0x00000011d6000000,0x00000011d6100000)]
  0x0000016741b50810 JavaThread "File watcher server" daemon [_thread_in_native, id=13752, stack(0x00000011d6900000,0x00000011d6a00000)]
  0x0000016741b4eec0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=36544, stack(0x00000011d6a00000,0x00000011d6b00000)]
  0x0000016741b4da80 JavaThread "jar transforms" [_thread_blocked, id=24124, stack(0x00000011d6e00000,0x00000011d6f00000)]
  0x0000016741b4f3d0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.13\fileContent)" [_thread_blocked, id=31608, stack(0x00000011d7000000,0x00000011d7100000)]
  0x0000016741b53090 JavaThread "jar transforms Thread 2" [_thread_blocked, id=39656, stack(0x00000011d6b00000,0x00000011d6c00000)]
  0x0000016741b544d0 JavaThread "Memory manager" [_thread_blocked, id=43160, stack(0x00000011d7200000,0x00000011d7300000)]
  0x0000016741d24f90 JavaThread "included builds" [_thread_blocked, id=12768, stack(0x00000011d7b00000,0x00000011d7c00000)]
  0x0000016741d2afc0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=35832, stack(0x00000011d8f00000,0x00000011d9000000)]
  0x0000016741d2a090 JavaThread "jar transforms Thread 4" [_thread_blocked, id=13372, stack(0x00000011d9000000,0x00000011d9100000)]
  0x0000016741d2a5a0 JavaThread "jar transforms Thread 5" [_thread_blocked, id=15052, stack(0x00000011d9100000,0x00000011d9200000)]
  0x0000016744bbebf0 JavaThread "jar transforms Thread 6" [_thread_blocked, id=4420, stack(0x00000011d9e00000,0x00000011d9f00000)]
  0x0000016744bbf610 JavaThread "jar transforms Thread 7" [_thread_blocked, id=15972, stack(0x00000011d9f00000,0x00000011da000000)]
  0x0000016741b4d060 JavaThread "jar transforms Thread 8" [_thread_blocked, id=20448, stack(0x00000011da000000,0x00000011da100000)]
  0x00000167455d17e0 JavaThread "jar transforms Thread 9" [_thread_blocked, id=28892, stack(0x00000011da200000,0x00000011da300000)]
  0x00000167455ce540 JavaThread "jar transforms Thread 10" [_thread_blocked, id=17392, stack(0x00000011da300000,0x00000011da400000)]
  0x00000167455d08b0 JavaThread "jar transforms Thread 11" [_thread_blocked, id=22876, stack(0x00000011da400000,0x00000011da500000)]
  0x00000167455cf470 JavaThread "jar transforms Thread 12" [_thread_blocked, id=27884, stack(0x00000011da500000,0x00000011da600000)]
  0x00000167455d5ec0 JavaThread "jar transforms Thread 13" [_thread_blocked, id=37308, stack(0x00000011d9800000,0x00000011d9900000)]
  0x00000167455d2c20 JavaThread "jar transforms Thread 14" [_thread_blocked, id=17352, stack(0x00000011da100000,0x00000011da200000)]
  0x00000167455d3b50 JavaThread "jar transforms Thread 15" [_thread_blocked, id=30800, stack(0x00000011da600000,0x00000011da700000)]
  0x00000167455d4570 JavaThread "jar transforms Thread 16" [_thread_blocked, id=42228, stack(0x00000011da700000,0x00000011da800000)]
  0x00000167455d59b0 JavaThread "jar transforms Thread 17" [_thread_blocked, id=44616, stack(0x00000011daa00000,0x00000011dab00000)]
  0x00000167455d3130 JavaThread "jar transforms Thread 18" [_thread_blocked, id=15708, stack(0x00000011dab00000,0x00000011dac00000)]
  0x00000167455d54a0 JavaThread "jar transforms Thread 19" [_thread_blocked, id=16900, stack(0x00000011dac00000,0x00000011dad00000)]
  0x0000016745ed2890 JavaThread "jar transforms Thread 20" [_thread_blocked, id=36176, stack(0x00000011dad00000,0x00000011dae00000)]
  0x0000016745ed0520 JavaThread "VFS cleanup" [_thread_blocked, id=9404, stack(0x00000011d5a00000,0x00000011d5b00000)]
  0x0000016745ed2380 JavaThread "Handler for socket connection from /127.0.0.1:8433 to /127.0.0.1:8701" [_thread_in_native, id=42492, stack(0x00000011d2c00000,0x00000011d2d00000)]
  0x0000016745ed1e70 JavaThread "Cancel handler" [_thread_blocked, id=36616, stack(0x00000011d2d00000,0x00000011d2e00000)]
  0x0000016745ecf0e0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:8433 to /127.0.0.1:8701" [_thread_blocked, id=31924, stack(0x00000011d4000000,0x00000011d4100000)]
  0x0000016745ed0f40 JavaThread "Stdin handler" [_thread_blocked, id=18536, stack(0x00000011d4200000,0x00000011d4300000)]
  0x0000016745ed0010 JavaThread "Daemon client event forwarder" [_thread_blocked, id=36060, stack(0x00000011d5800000,0x00000011d5900000)]
  0x0000016745ecf5f0 JavaThread "Cache worker for file hash cache (E:\StudioProjects\AIOSService\.gradle\8.13\fileHashes)" [_thread_blocked, id=34800, stack(0x00000011d5b00000,0x00000011d5c00000)]
  0x0000016745ed1450 JavaThread "Cache worker for Build Output Cleanup Cache (E:\StudioProjects\AIOSService\.gradle\buildOutputCleanup)" [_thread_blocked, id=33312, stack(0x00000011d5c00000,0x00000011d5d00000)]
  0x0000016745ed1960 JavaThread "Cache worker for Build Output Cleanup Cache (E:\StudioProjects\AIOSService\buildSrc\.gradle\buildOutputCleanup)" [_thread_blocked, id=23284, stack(0x00000011d5f00000,0x00000011d6000000)]
  0x0000016741d24a80 JavaThread "Cache worker for checksums cache (E:\StudioProjects\AIOSService\.gradle\8.13\checksums)" [_thread_blocked, id=31308, stack(0x00000011d6400000,0x00000011d6500000)]
  0x0000016741d25ec0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.13\md-supplier)" [_thread_blocked, id=27804, stack(0x00000011d6500000,0x00000011d6600000)]
  0x0000016741d254a0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.13\md-rule)" [_thread_blocked, id=8340, stack(0x00000011d6c00000,0x00000011d6d00000)]
  0x0000016741d259b0 JavaThread "Unconstrained build operations" [_thread_blocked, id=43384, stack(0x00000011d7100000,0x00000011d7200000)]
  0x0000016741d263d0 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=34304, stack(0x00000011d7300000,0x00000011d7400000)]
  0x0000016741d24060 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=24020, stack(0x00000011d7400000,0x00000011d7500000)]
  0x0000016741d28230 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=10368, stack(0x00000011d7500000,0x00000011d7600000)]
  0x0000016741d28740 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=42772, stack(0x00000011d7600000,0x00000011d7700000)]
  0x0000016741d29670 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=7272, stack(0x00000011d7700000,0x00000011d7800000)]
  0x0000016741d28c50 JavaThread "build event listener" [_thread_blocked, id=41116, stack(0x00000011d7900000,0x00000011d7a00000)]
  0x0000016741d2aab0 JavaThread "included builds" [_thread_blocked, id=11868, stack(0x00000011d7a00000,0x00000011d7b00000)]
  0x0000016741d29160 JavaThread "included builds Thread 2" [_thread_blocked, id=2456, stack(0x00000011d7e00000,0x00000011d7f00000)]
  0x0000016741d29b80 JavaThread "Execution worker" [_thread_blocked, id=11200, stack(0x00000011d7f00000,0x00000011d8000000)]
  0x0000016741d27300 JavaThread "Execution worker Thread 2" [_thread_blocked, id=21168, stack(0x00000011d8000000,0x00000011d8100000)]
  0x0000016741d27810 JavaThread "Execution worker Thread 3" [_thread_blocked, id=10024, stack(0x00000011d8100000,0x00000011d8200000)]
  0x0000016741d27d20 JavaThread "Cache worker for execution history cache (E:\StudioProjects\AIOSService\buildSrc\.gradle\8.13\executionHistory)" [_thread_blocked, id=32404, stack(0x00000011d8200000,0x00000011d8300000)]
  0x0000016741d2b9e0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=33612, stack(0x00000011d8300000,0x00000011d8400000)]
  0x0000016741b51740 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=34228, stack(0x00000011d8400000,0x00000011d8500000)]
  0x0000016741b4df90 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=24436, stack(0x00000011d8500000,0x00000011d8600000)]
  0x0000016741b4e9b0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=20296, stack(0x00000011d8600000,0x00000011d8700000)]
  0x0000016741b52670 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=26488, stack(0x00000011d8700000,0x00000011d8800000)]
  0x0000016741b52b80 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=26276, stack(0x00000011d8800000,0x00000011d8900000)]
  0x0000016741b535a0 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=26060, stack(0x00000011d8900000,0x00000011d8a00000)]
  0x0000016741b4fdf0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=37740, stack(0x00000011d8a00000,0x00000011d8b00000)]
  0x0000016741b53ab0 JavaThread "Problems report writer" [_thread_blocked, id=14580, stack(0x00000011d8b00000,0x00000011d8c00000)]
  0x0000016741b50300 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=29056, stack(0x00000011d8c00000,0x00000011d8d00000)]
  0x0000016741fe6140 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=39612, stack(0x00000011d8d00000,0x00000011d8e00000)]
  0x0000016741fe33b0 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=22004, stack(0x00000011d8e00000,0x00000011d8f00000)]
  0x0000016741fe6650 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=32104, stack(0x00000011d9200000,0x00000011d9300000)]
  0x0000016741fe38c0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=31312, stack(0x00000011d9300000,0x00000011d9400000)]
  0x0000016741fe6b60 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=33596, stack(0x00000011d9400000,0x00000011d9500000)]
  0x0000016741fe42e0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=12432, stack(0x00000011d9900000,0x00000011d9a00000)]
  0x0000016741fe47f0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=17236, stack(0x00000011d9a00000,0x00000011d9b00000)]
  0x0000016744bbdcc0 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=21564, stack(0x00000011d9b00000,0x00000011d9c00000)]
  0x0000016744bbf100 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=876, stack(0x00000011da800000,0x00000011da900000)]
  0x0000016744bc0030 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=24816, stack(0x00000011da900000,0x00000011daa00000)]
  0x0000016744bc0f60 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=36656, stack(0x00000011dae00000,0x00000011daf00000)]
  0x0000016744bc0540 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=26508, stack(0x00000011daf00000,0x00000011db000000)]
  0x0000016744bc0a50 JavaThread "ForkJoinPool.commonPool-worker-8" daemon [_thread_blocked, id=16788, stack(0x00000011db000000,0x00000011db100000)]
  0x0000016744bbd7b0 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=36748, stack(0x00000011db100000,0x00000011db200000)]
  0x0000016744bbe1d0 JavaThread "ForkJoinPool.commonPool-worker-10" daemon [_thread_blocked, id=22976, stack(0x00000011db200000,0x00000011db300000)]
  0x000001677f60edf0 JavaThread "Exec process" daemon [_thread_blocked, id=10896, stack(0x00000011db300000,0x00000011db400000)]
  0x000001677f60f300 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=39476, stack(0x00000011db400000,0x00000011db500000)]
  0x000001677f60f810 JavaThread "Exec process Thread 2" daemon [_thread_blocked, id=31920, stack(0x00000011db500000,0x00000011db600000)]
  0x000001677f610230 JavaThread "Exec process Thread 3" daemon [_thread_blocked, id=34232, stack(0x00000011db600000,0x00000011db700000)]
  0x000001677f610c50 JavaThread "ForkJoinPool.commonPool-worker-12" daemon [_thread_blocked, id=41964, stack(0x00000011db700000,0x00000011db800000)]
  0x00000167455d4a80 JavaThread "ForkJoinPool.commonPool-worker-13" daemon [_thread_blocked, id=6964, stack(0x00000011db800000,0x00000011db900000)]
  0x0000016747d7dcc0 JavaThread "ForkJoinPool.commonPool-worker-14" daemon [_thread_blocked, id=16340, stack(0x00000011db900000,0x00000011dba00000)]
  0x0000016747d7e6e0 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=27604, stack(0x00000011dba00000,0x00000011dbb00000)]
  0x0000016747d7f100 JavaThread "ForkJoinPool.commonPool-worker-16" daemon [_thread_blocked, id=39000, stack(0x00000011dbb00000,0x00000011dbc00000)]
  0x0000016747d7f610 JavaThread "ForkJoinPool.commonPool-worker-17" daemon [_thread_blocked, id=30596, stack(0x00000011dbc00000,0x00000011dbd00000)]
  0x0000016747d7d7b0 JavaThread "ForkJoinPool.commonPool-worker-18" daemon [_thread_blocked, id=14600, stack(0x00000011d9500000,0x00000011d9600000)]
  0x0000016747d7c880 JavaThread "Cache worker for execution history cache (E:\StudioProjects\AIOSService\.gradle\8.13\executionHistory)" [_thread_blocked, id=32424, stack(0x00000011d9600000,0x00000011d9700000)]
  0x0000016747d7cd90 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=43496, stack(0x00000011d9700000,0x00000011d9800000)]
  0x0000016741ca91b0 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=34852, stack(0x00000011dbd00000,0x00000011dbe00000)]
  0x0000016741ca7860 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=32980, stack(0x00000011dc100000,0x00000011dc200000)]
  0x0000016741ca8790 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=27808, stack(0x00000011dc200000,0x00000011dc300000)]
  0x0000016741cab520 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=31576, stack(0x00000011dc300000,0x00000011dc400000)]
  0x0000016741ca8ca0 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=33284, stack(0x00000011dc400000,0x00000011dc500000)]
  0x0000016741ca96c0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=43260, stack(0x00000011dc500000,0x00000011dc600000)]
  0x0000016741ca9bd0 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=7748, stack(0x00000011dc600000,0x00000011dc700000)]
  0x0000016741ca7d70 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=3576, stack(0x00000011d5900000,0x00000011d5a00000)]
  0x0000016741ca8280 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=26068, stack(0x00000011d6d00000,0x00000011d6e00000)]
  0x0000016741caa0e0 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=44044, stack(0x00000011dbf00000,0x00000011dc000000)]
  0x0000016741cadda0 JavaThread "WorkerExecutor Queue Thread 2" [_thread_in_Java, id=43884, stack(0x00000011dc000000,0x00000011dc100000)]
  0x0000016741cae2b0 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=9160, stack(0x00000011dc700000,0x00000011dc800000)]
  0x0000016741caab00 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=34404, stack(0x00000011dca00000,0x00000011dcb00000)]
  0x0000016741cac450 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=42696, stack(0x00000011dcb00000,0x00000011dcc00000)]
  0x0000016741caba30 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=31484, stack(0x00000011dcc00000,0x00000011dcd00000)]
  0x0000016741cac960 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=17928, stack(0x00000011dce00000,0x00000011dcf00000)]
  0x0000016741cace70 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=32020, stack(0x00000011dcf00000,0x00000011dd000000)]
  0x0000016741cae7c0 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=37248, stack(0x00000011dd000000,0x00000011dd100000)]
  0x0000016741caecd0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=42924, stack(0x00000011dd100000,0x00000011dd200000)]
  0x0000016741cabf40 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=41160, stack(0x00000011dd200000,0x00000011dd300000)]
  0x0000016741caf1e0 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=12284, stack(0x00000011dd300000,0x00000011dd400000)]
  0x0000016741cad380 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=8404, stack(0x00000011dd500000,0x00000011dd600000)]
  0x00000167437d3730 JavaThread "WorkerExecutor Queue Thread 4" [_thread_blocked, id=25676, stack(0x00000011dd600000,0x00000011dd700000)]
  0x00000167437d4660 JavaThread "Incoming local TCP Connector on port 8767" [_thread_blocked, id=18072, stack(0x00000011d2e00000,0x00000011d2f00000)]
  0x00000167437d0eb0 JavaThread "Exec process" [_thread_in_native, id=11740, stack(0x00000011dd700000,0x00000011dd800000)]
  0x00000167437d2d10 JavaThread "pool-4-thread-1" [_thread_blocked, id=8808, stack(0x00000011dd800000,0x00000011dd900000)]
  0x00000167437d4b70 JavaThread "Exec process Thread 2" [_thread_blocked, id=32684, stack(0x00000011dda00000,0x00000011ddb00000)]
  0x00000167437d3220 JavaThread "Exec process Thread 3" [_thread_in_native, id=44308, stack(0x00000011ddb00000,0x00000011ddc00000)]
  0x00000167437d5080 JavaThread "Exec process Thread 4" [_thread_in_native, id=18424, stack(0x00000011ddc00000,0x00000011ddd00000)]
  0x00000167437d5aa0 JavaThread "stderr" [_thread_in_native, id=41064, stack(0x00000011ddd00000,0x00000011dde00000)]
  0x00000167437d5590 JavaThread "stdout" [_thread_in_native, id=40220, stack(0x00000011dde00000,0x00000011ddf00000)]
  0x00000167437d3c40 JavaThread "/127.0.0.1:8767 to /127.0.0.1:8779 workers" [_thread_blocked, id=15428, stack(0x00000011d6200000,0x00000011d6300000)]
  0x00000167437d5fb0 JavaThread "/127.0.0.1:8767 to /127.0.0.1:8779 workers Thread 2" [_thread_blocked, id=26372, stack(0x00000011d6300000,0x00000011d6400000)]
  0x00000167437d2800 JavaThread "/127.0.0.1:8767 to /127.0.0.1:8779 workers Thread 3" [_thread_blocked, id=2796, stack(0x00000011d6f00000,0x00000011d7000000)]
  0x00000167437d6ee0 JavaThread "/127.0.0.1:8767 to /127.0.0.1:8779 workers Thread 4" [_thread_blocked, id=21396, stack(0x00000011d7800000,0x00000011d7900000)]
  0x00000167437d73f0 JavaThread "/127.0.0.1:8767 to /127.0.0.1:8779 workers Thread 5" [_thread_in_native, id=41364, stack(0x00000011dbe00000,0x00000011dbf00000)]
  0x00000167456772a0 JavaThread "C1 CompilerThread1" daemon [_thread_in_native, id=26308, stack(0x00000011dc800000,0x00000011dc900000)]
  0x0000016745678d30 JavaThread "C1 CompilerThread2" daemon [_thread_in_native, id=12368, stack(0x00000011dc900000,0x00000011dca00000)]
  0x00000167456787e0 JavaThread "C1 CompilerThread3" daemon [_thread_in_native, id=38556, stack(0x00000011dcd00000,0x00000011dce00000)]
  0x0000016745675d60 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=32484, stack(0x00000011d6100000,0x00000011d6200000)]
  0x0000016745675810 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=30528, stack(0x00000011dd400000,0x00000011dd500000)]
  0x0000016745679d20 JavaThread "C2 CompilerThread3" daemon [_thread_in_native, id=36408, stack(0x00000011dd900000,0x00000011dda00000)]
  0x0000016745676800 JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=21912, stack(0x00000011ddf00000,0x00000011de000000)]
=>0x000001674567c7a0 JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=540, stack(0x00000011de000000,0x00000011de100000)]
  0x000001674567a7c0 JavaThread "C2 CompilerThread6" daemon [_thread_in_native, id=22724, stack(0x00000011de100000,0x00000011de200000)]
  0x000001674567b260 JavaThread "C2 CompilerThread7" daemon [_thread_in_native, id=28196, stack(0x00000011de200000,0x00000011de300000)]
  0x00000167437d7e10 JavaThread "ForkJoinPool-1-worker-1" daemon [_thread_blocked, id=34244, stack(0x00000011de400000,0x00000011de500000)]
  0x00000167437d7900 JavaThread "ForkJoinPool-1-worker-2" daemon [_thread_blocked, id=21616, stack(0x00000011de500000,0x00000011de600000)]
  0x00000167437d8320 JavaThread "ForkJoinPool-1-worker-3" daemon [_thread_blocked, id=4164, stack(0x00000011de600000,0x00000011de700000)]
  0x00000167455d03a0 JavaThread "ForkJoinPool-1-worker-4" daemon [_thread_blocked, id=25464, stack(0x00000011de700000,0x00000011de800000)]
  0x0000016744f369d0 JavaThread "ForkJoinPool-1-worker-5" daemon [_thread_blocked, id=38416, stack(0x00000011de800000,0x00000011de900000)]
  0x0000016744f33730 JavaThread "ForkJoinPool-1-worker-6" daemon [_thread_blocked, id=26312, stack(0x00000011de900000,0x00000011dea00000)]
  0x0000016744f35fb0 JavaThread "ForkJoinPool-1-worker-7" daemon [_thread_blocked, id=40928, stack(0x00000011dea00000,0x00000011deb00000)]
  0x0000016744f33c40 JavaThread "ForkJoinPool-1-worker-8" daemon [_thread_blocked, id=34772, stack(0x00000011deb00000,0x00000011dec00000)]
  0x0000016744f33220 JavaThread "ForkJoinPool-1-worker-9" daemon [_thread_blocked, id=17480, stack(0x00000011dec00000,0x00000011ded00000)]
  0x0000016744f364c0 JavaThread "ForkJoinPool-1-worker-10" daemon [_thread_blocked, id=1908, stack(0x00000011ded00000,0x00000011dee00000)]
  0x0000016744f36ee0 JavaThread "ForkJoinPool-1-worker-11" daemon [_thread_blocked, id=44124, stack(0x00000011dee00000,0x00000011def00000)]
  0x0000016744f373f0 JavaThread "ForkJoinPool-1-worker-12" daemon [_thread_blocked, id=28600, stack(0x00000011def00000,0x00000011df000000)]
  0x0000016744f37900 JavaThread "ForkJoinPool-1-worker-13" daemon [_thread_blocked, id=17440, stack(0x00000011df000000,0x00000011df100000)]
  0x0000016744f35590 JavaThread "ForkJoinPool-1-worker-14" daemon [_thread_blocked, id=32468, stack(0x00000011df100000,0x00000011df200000)]
  0x0000016744f34150 JavaThread "ForkJoinPool-1-worker-15" daemon [_thread_blocked, id=1900, stack(0x00000011df200000,0x00000011df300000)]
  0x0000016744f37e10 JavaThread "ForkJoinPool-1-worker-16" daemon [_thread_blocked, id=31420, stack(0x00000011df300000,0x00000011df400000)]
  0x0000016744f34660 JavaThread "ForkJoinPool-1-worker-17" daemon [_thread_blocked, id=38440, stack(0x00000011df400000,0x00000011df500000)]
  0x0000016744f38320 JavaThread "ForkJoinPool-1-worker-18" daemon [_thread_blocked, id=31528, stack(0x00000011df500000,0x00000011df600000)]
  0x0000016744f34b70 JavaThread "ForkJoinPool-1-worker-19" daemon [_thread_blocked, id=40636, stack(0x00000011df600000,0x00000011df700000)]
  0x0000016744f35080 JavaThread "ForkJoinPool-1-worker-20" daemon [_thread_blocked, id=39652, stack(0x00000011df700000,0x00000011df800000)]

Other Threads:
  0x000001677d2abbd0 VMThread "VM Thread" [stack: 0x00000011d3600000,0x00000011d3700000] [id=34148]
  0x000001677dcdf0f0 WatcherThread [stack: 0x00000011d4500000,0x00000011d4600000] [id=44668]
  0x00000167582f3b90 GCTaskThread "GC Thread#0" [stack: 0x00000011d3000000,0x00000011d3100000] [id=17540]
  0x000001677e6035e0 GCTaskThread "GC Thread#1" [stack: 0x00000011d4900000,0x00000011d4a00000] [id=22760]
  0x000001677e6038a0 GCTaskThread "GC Thread#2" [stack: 0x00000011d4a00000,0x00000011d4b00000] [id=33764]
  0x000001677e5c3370 GCTaskThread "GC Thread#3" [stack: 0x00000011d4b00000,0x00000011d4c00000] [id=16548]
  0x000001677e70dc10 GCTaskThread "GC Thread#4" [stack: 0x00000011d4c00000,0x00000011d4d00000] [id=26464]
  0x000001677e70e6e0 GCTaskThread "GC Thread#5" [stack: 0x00000011d4d00000,0x00000011d4e00000] [id=17188]
  0x000001677e0b5a70 GCTaskThread "GC Thread#6" [stack: 0x00000011d4e00000,0x00000011d4f00000] [id=41072]
  0x000001677e0b5d30 GCTaskThread "GC Thread#7" [stack: 0x00000011d4f00000,0x00000011d5000000] [id=34768]
  0x000001677e0b5ff0 GCTaskThread "GC Thread#8" [stack: 0x00000011d5000000,0x00000011d5100000] [id=34140]
  0x000001677e647c90 GCTaskThread "GC Thread#9" [stack: 0x00000011d5100000,0x00000011d5200000] [id=38188]
  0x000001677e0b62b0 GCTaskThread "GC Thread#10" [stack: 0x00000011d5200000,0x00000011d5300000] [id=30560]
  0x000001677e0b6980 GCTaskThread "GC Thread#11" [stack: 0x00000011d5300000,0x00000011d5400000] [id=43204]
  0x000001677ea390e0 GCTaskThread "GC Thread#12" [stack: 0x00000011d4300000,0x00000011d4400000] [id=25756]
  0x000001677ea393a0 GCTaskThread "GC Thread#13" [stack: 0x00000011d4600000,0x00000011d4700000] [id=30656]
  0x000001677ea39660 GCTaskThread "GC Thread#14" [stack: 0x00000011d4700000,0x00000011d4800000] [id=21184]
  0x00000167582f69f0 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000011d3100000,0x00000011d3200000] [id=39156]
  0x00000167582f7480 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000011d3200000,0x00000011d3300000] [id=10884]
  0x000001677ea3a6e0 ConcurrentGCThread "G1 Conc#1" [stack: 0x00000011d6600000,0x00000011d6700000] [id=15332]
  0x000001677ea3a9a0 ConcurrentGCThread "G1 Conc#2" [stack: 0x00000011d6700000,0x00000011d6800000] [id=21756]
  0x000001677ea3ac60 ConcurrentGCThread "G1 Conc#3" [stack: 0x00000011d6800000,0x00000011d6900000] [id=15932]
  0x000001675832e5c0 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000011d3300000,0x00000011d3400000] [id=40176]
  0x00000167443cc1a0 ConcurrentGCThread "G1 Refine#1" [stack: 0x00000011d7c00000,0x00000011d7d00000] [id=21164]
  0x00000167443cd630 ConcurrentGCThread "G1 Refine#2" [stack: 0x00000011d7d00000,0x00000011d7e00000] [id=22208]
  0x0000016749389840 ConcurrentGCThread "G1 Refine#3" [stack: 0x00000011df800000,0x00000011df900000] [id=30116]
  0x0000016749389b30 ConcurrentGCThread "G1 Refine#4" [stack: 0x00000011df900000,0x00000011dfa00000] [id=15420]
  0x0000016749386940 ConcurrentGCThread "G1 Refine#5" [stack: 0x00000011dfa00000,0x00000011dfb00000] [id=35724]
  0x0000016749386c30 ConcurrentGCThread "G1 Refine#6" [stack: 0x00000011dfb00000,0x00000011dfc00000] [id=43420]
  0x0000016749386f20 ConcurrentGCThread "G1 Refine#7" [stack: 0x00000011dfc00000,0x00000011dfd00000] [id=16716]
  0x0000016749387210 ConcurrentGCThread "G1 Refine#8" [stack: 0x00000011dfd00000,0x00000011dfe00000] [id=15304]
  0x00000167493877f0 ConcurrentGCThread "G1 Refine#9" [stack: 0x00000011dfe00000,0x00000011dff00000] [id=3116]
  0x00000167443ceac0 ConcurrentGCThread "G1 Refine#10" [stack: 0x00000011dff00000,0x00000011e0000000] [id=38380]
  0x00000167443cedb0 ConcurrentGCThread "G1 Refine#11" [stack: 0x00000011e0000000,0x00000011e0100000] [id=31144]
  0x000001677df983a0 ConcurrentGCThread "G1 Refine#12" [stack: 0x00000011e0100000,0x00000011e0200000] [id=36036]
  0x000001675832e9b0 ConcurrentGCThread "G1 Service" [stack: 0x00000011d3400000,0x00000011d3500000] [id=44356]
  0x000001677d2a6a70 ConcurrentGCThread "StringDedupProcessor" [stack: 0x00000011d3500000,0x00000011d3600000] [id=35400]

Threads with active compile tasks:
C2 CompilerThread0   104110 21061       4       com.android.tools.r8.dex.B::a (836 bytes)
C1 CompilerThread0   104111 22374       2       com.android.tools.r8.internal.Ph::g (13 bytes)
C1 CompilerThread1   104112 22459       3       com.android.tools.r8.graph.B0$d::a (99 bytes)
C1 CompilerThread2   104113 22453       2       com.android.tools.r8.graph.q2::a (349 bytes)
C1 CompilerThread3   104114 22389       2       com.android.tools.r8.dex.code.z3::getField (8 bytes)
C2 CompilerThread1   104115 21051 %     4       com.android.tools.r8.dex.B::a @ 316 (1021 bytes)
C2 CompilerThread2   104116 22296       4       java.util.concurrent.ForkJoinPool::scan (235 bytes)
C2 CompilerThread3   104117 22097       4       com.android.tools.r8.graph.q0::<init> (339 bytes)
C2 CompilerThread4   104118 22123       4       com.android.tools.r8.graph.H5$$Lambda$3325/0x000001670a2031a0::apply (12 bytes)
C2 CompilerThread5   104119 22126       4       com.android.tools.r8.graph.H5::a (32 bytes)
C2 CompilerThread6   104120 21164       4       com.android.tools.r8.dex.code.O::a (3284 bytes)
C2 CompilerThread7   104121 22125       4       com.android.tools.r8.graph.H5::a (178 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000600000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000016708000000-0x0000016708bb0000-0x0000016708bb0000), size 12255232, SharedBaseAddress: 0x0000016708000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000016709000000-0x000001673d000000, reserved size: 872415232
Narrow klass base: 0x0000016708000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 20 total, 20 available
 Memory: 32492M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 32M
 Heap Min Capacity: 32M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8G
 Pre-touch: Disabled
 Parallel Workers: 15
 Concurrent Workers: 4
 Concurrent Refinement Workers: 15
 Periodic GC: Disabled
 String Deduplication is enabled

Heap:
 garbage-first heap   total 524288K, used 301277K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 129624K, committed 130752K, reserved 983040K
  class space    used 17952K, committed 18496K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000600000000, 0x0000000602000000, 0x0000000602000000|100%| O|  |TAMS 0x0000000600000000, 0x0000000600000000| Untracked 
|   1|0x0000000602000000, 0x0000000604000000, 0x0000000604000000|100%| O|  |TAMS 0x0000000604000000, 0x0000000602000000| Untracked 
|   2|0x0000000604000000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|   3|0x0000000606000000, 0x0000000608000000, 0x0000000608000000|100%| O|  |TAMS 0x0000000608000000, 0x0000000606000000| Untracked 
|   4|0x0000000608000000, 0x000000060a000000, 0x000000060a000000|100%| O|  |TAMS 0x0000000609dcbe00, 0x0000000608000000| Untracked 
|   5|0x000000060a000000, 0x000000060c000000, 0x000000060c000000|100%| O|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|   6|0x000000060c000000, 0x000000060e000000, 0x000000060e000000|100%| O|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|   7|0x000000060e000000, 0x000000060ebd9200, 0x0000000610000000| 37%| O|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|   8|0x0000000610000000, 0x0000000610000000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|   9|0x0000000612000000, 0x0000000612000000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  10|0x0000000614000000, 0x0000000614000000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  11|0x0000000616000000, 0x0000000616000000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  12|0x0000000618000000, 0x0000000618000000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  13|0x000000061a000000, 0x000000061ba5e390, 0x000000061c000000| 82%| S|CS|TAMS 0x000000061a000000, 0x000000061a000000| Complete 
|  14|0x000000061c000000, 0x000000061dcbecc8, 0x000000061e000000| 89%| E|  |TAMS 0x000000061c000000, 0x000000061c000000| Complete 
|  15|0x000000061e000000, 0x0000000620000000, 0x0000000620000000|100%| E|CS|TAMS 0x000000061e000000, 0x000000061e000000| Complete 

Card table byte_map: [0x0000016770860000,0x0000016771860000] _byte_map_base: 0x000001676d860000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000167582f4100, (CMBitMap*) 0x00000167582f40c0
 Prev Bits: [0x0000016700000000, 0x0000016708000000)
 Next Bits: [0x0000016772860000, 0x000001677a860000)

Polling page: 0x0000016755ea0000

Metaspace:

Usage:
  Non-class:    109.05 MB used.
      Class:     17.53 MB used.
       Both:    126.59 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     109.62 MB ( 86%) committed,  2 nodes.
      Class space:      832.00 MB reserved,      18.06 MB (  2%) committed,  1 nodes.
             Both:      960.00 MB reserved,     127.69 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  2.03 MB
       Class:  13.95 MB
        Both:  15.98 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 178.44 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 21.
num_arena_births: 1754.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2042.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 21.
num_chunks_taken_from_freelist: 7526.
num_chunk_merges: 21.
num_chunk_splits: 4902.
num_chunks_enlarged: 3167.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=11757Kb max_used=11757Kb free=107411Kb
 bounds [0x0000016767950000, 0x00000167684d0000, 0x000001676edb0000]
CodeHeap 'profiled nmethods': size=119104Kb used=36232Kb max_used=36232Kb free=82871Kb
 bounds [0x000001675fdb0000, 0x0000016762120000, 0x0000016767200000]
CodeHeap 'non-nmethods': size=7488Kb used=4154Kb max_used=4256Kb free=3333Kb
 bounds [0x0000016767200000, 0x0000016767630000, 0x0000016767950000]
 total_blobs=19893 nmethods=18877 adapters=923
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 102.815 Thread 0x00000167456772a0 22305       3       com.android.tools.r8.dex.r0$$Lambda$2847/0x000001670a14e230::accept (20 bytes)
Event: 102.815 Thread 0x00000167456787e0 nmethod 22299 0x00000167620d0e10 code [0x00000167620d1040, 0x00000167620d14a8]
Event: 102.816 Thread 0x0000016745678d30 22306       3       com.android.tools.r8.internal.zG::a (9 bytes)
Event: 102.817 Thread 0x000001677da874e0 22307       3       com.android.tools.r8.utils.u0::a (105 bytes)
Event: 102.817 Thread 0x00000167456772a0 nmethod 22305 0x00000167620d1b90 code [0x00000167620d1da0, 0x00000167620d24a8]
Event: 102.818 Thread 0x0000016745678d30 nmethod 22306 0x00000167620d2790 code [0x00000167620d2940, 0x00000167620d2b28]
Event: 102.821 Thread 0x00000167456772a0 22308       1       com.android.tools.r8.internal.jg::b (2 bytes)
Event: 102.821 Thread 0x0000016745678d30 22309       2       com.android.tools.r8.dex.n0$$Lambda$2849/0x000001670a14eb08::test (12 bytes)
Event: 102.821 Thread 0x00000167456787e0 22310       2       com.android.tools.r8.dex.n0::a (9 bytes)
Event: 102.943 Thread 0x00000167456772a0 nmethod 22308 0x00000167684c9d90 code [0x00000167684c9f20, 0x00000167684c9ff8]
Event: 102.943 Thread 0x00000167456787e0 nmethod 22310 0x00000167620d3f10 code [0x00000167620d40a0, 0x00000167620d4198]
Event: 102.943 Thread 0x0000016745678d30 nmethod 22309 0x00000167620d4290 code [0x00000167620d4420, 0x00000167620d4578]
Event: 102.943 Thread 0x00000167456772a0 22311       2       com.android.tools.r8.dex.n0$$Lambda$2848/0x000001670a14e870::compare (16 bytes)
Event: 102.943 Thread 0x00000167456787e0 22312       2       com.android.tools.r8.dex.n0::a (94 bytes)
Event: 102.944 Thread 0x000001677da874e0 nmethod 22307 0x00000167620d2c10 code [0x00000167620d2ec0, 0x00000167620d3a68]
Event: 102.945 Thread 0x00000167456772a0 nmethod 22311 0x00000167620d4690 code [0x00000167620d4840, 0x00000167620d49e8]
Event: 102.949 Thread 0x00000167456787e0 nmethod 22312 0x00000167620d4b10 code [0x00000167620d4d60, 0x00000167620d5248]
Event: 102.955 Thread 0x0000016745675810 nmethod 22294 0x00000167684ca090 code [0x00000167684ca220, 0x00000167684ca3e8]
Event: 102.958 Thread 0x0000016745675810 22296       4       java.util.concurrent.ForkJoinPool::scan (235 bytes)
Event: 103.706 Thread 0x00000167456787e0 22315       1       java.util.concurrent.ThreadPoolExecutor::getQueue (5 bytes)

GC Heap History (20 events):
Event: 88.521 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 327680K, used 181910K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 2 young (65536K), 1 survivors (32768K)
 Metaspace       used 109257K, committed 110400K, reserved 983040K
  class space    used 15117K, committed 15680K, reserved 851968K
}
Event: 88.545 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 327680K, used 144195K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 109257K, committed 110400K, reserved 983040K
  class space    used 15117K, committed 15680K, reserved 851968K
}
Event: 90.176 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 327680K, used 209731K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 4 young (131072K), 1 survivors (32768K)
 Metaspace       used 110362K, committed 111488K, reserved 983040K
  class space    used 15233K, committed 15808K, reserved 851968K
}
Event: 90.193 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 327680K, used 147845K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 110362K, committed 111488K, reserved 983040K
  class space    used 15233K, committed 15808K, reserved 851968K
}
Event: 91.528 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 327680K, used 180613K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 111811K, committed 112960K, reserved 983040K
  class space    used 15438K, committed 16000K, reserved 851968K
}
Event: 91.541 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 327680K, used 150737K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 111811K, committed 112960K, reserved 983040K
  class space    used 15438K, committed 16000K, reserved 851968K
}
Event: 93.867 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 327680K, used 216273K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 118956K, committed 120064K, reserved 983040K
  class space    used 16392K, committed 16960K, reserved 851968K
}
Event: 93.881 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 327680K, used 152424K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 118956K, committed 120064K, reserved 983040K
  class space    used 16392K, committed 16960K, reserved 851968K
}
Event: 96.201 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 327680K, used 217960K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 122595K, committed 123712K, reserved 983040K
  class space    used 16956K, committed 17536K, reserved 851968K
}
Event: 96.214 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 327680K, used 155362K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 122595K, committed 123712K, reserved 983040K
  class space    used 16956K, committed 17536K, reserved 851968K
}
Event: 98.426 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 327680K, used 188130K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 125981K, committed 127104K, reserved 983040K
  class space    used 17342K, committed 17920K, reserved 851968K
}
Event: 98.441 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 327680K, used 162843K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 125981K, committed 127104K, reserved 983040K
  class space    used 17342K, committed 17920K, reserved 851968K
}
Event: 99.057 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 327680K, used 195611K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 126883K, committed 128000K, reserved 983040K
  class space    used 17469K, committed 17984K, reserved 851968K
}
Event: 99.075 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 327680K, used 186927K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 126883K, committed 128000K, reserved 983040K
  class space    used 17469K, committed 17984K, reserved 851968K
}
Event: 99.420 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 327680K, used 219695K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 2 young (65536K), 1 survivors (32768K)
 Metaspace       used 127254K, committed 128384K, reserved 983040K
  class space    used 17522K, committed 18048K, reserved 851968K
}
Event: 99.444 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 524288K, used 198268K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 127254K, committed 128384K, reserved 983040K
  class space    used 17522K, committed 18048K, reserved 851968K
}
Event: 100.371 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 524288K, used 296572K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 4 young (131072K), 1 survivors (32768K)
 Metaspace       used 128717K, committed 129792K, reserved 983040K
  class space    used 17834K, committed 18368K, reserved 851968K
}
Event: 100.415 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 524288K, used 240125K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 128717K, committed 129792K, reserved 983040K
  class space    used 17834K, committed 18368K, reserved 851968K
}
Event: 101.248 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 524288K, used 305661K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 3 young (98304K), 1 survivors (32768K)
 Metaspace       used 129028K, committed 130176K, reserved 983040K
  class space    used 17871K, committed 18432K, reserved 851968K
}
Event: 101.291 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 524288K, used 268509K [0x0000000600000000, 0x0000000800000000)
  region size 32768K, 1 young (32768K), 1 survivors (32768K)
 Metaspace       used 129028K, committed 130176K, reserved 983040K
  class space    used 17871K, committed 18432K, reserved 851968K
}

Dll operation events (15 events):
Event: 0.023 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.dll
Event: 0.056 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jsvml.dll
Event: 0.178 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
Event: 0.192 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\instrument.dll
Event: 0.202 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\net.dll
Event: 0.206 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\nio.dll
Event: 0.211 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
Event: 0.874 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jimage.dll
Event: 1.257 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\verify.dll
Event: 1.551 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 1.589 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
Event: 4.598 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management.dll
Event: 4.606 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management_ext.dll
Event: 6.076 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\extnet.dll
Event: 6.712 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 102.478 Thread 0x0000016741cadda0 DEOPT PACKING pc=0x00000167680cf960 sp=0x00000011dc0fdb30
Event: 102.478 Thread 0x0000016741cadda0 DEOPT UNPACKING pc=0x00000167672569a3 sp=0x00000011dc0fda68 mode 2
Event: 102.675 Thread 0x00000167437d8320 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011de6fea40
Event: 102.675 Thread 0x00000167437d8320 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011de6fdec8 mode 0
Event: 102.675 Thread 0x0000016744f373f0 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011deffe8e0
Event: 102.675 Thread 0x0000016744f373f0 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011deffdd68 mode 0
Event: 102.676 Thread 0x0000016744f37900 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011df0fec50
Event: 102.676 Thread 0x0000016744f37900 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011df0fe0d8 mode 0
Event: 102.676 Thread 0x0000016744f37900 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011df0fec50
Event: 102.677 Thread 0x0000016744f37900 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011df0fe0d8 mode 0
Event: 102.678 Thread 0x00000167437d7900 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011de5fed30
Event: 102.678 Thread 0x00000167437d7900 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011de5fe1b8 mode 0
Event: 102.678 Thread 0x0000016744f37900 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011df0fec50
Event: 102.678 Thread 0x0000016744f37900 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011df0fe0d8 mode 0
Event: 102.679 Thread 0x0000016744f36ee0 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011deefeca0
Event: 102.679 Thread 0x0000016744f36ee0 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011deefe128 mode 0
Event: 102.679 Thread 0x00000167455d03a0 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011de7feb50
Event: 102.679 Thread 0x00000167455d03a0 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011de7fdfd8 mode 0
Event: 102.679 Thread 0x0000016744f33c40 DEOPT PACKING pc=0x00000167620c9b22 sp=0x00000011debfee80
Event: 102.679 Thread 0x0000016744f33c40 DEOPT UNPACKING pc=0x0000016767257143 sp=0x00000011debfe308 mode 0

Classes loaded (20 events):
Event: 98.883 Loading class javax/management/AttributeNotFoundException
Event: 98.883 Loading class javax/management/AttributeNotFoundException done
Event: 98.883 Loading class javax/management/ReflectionException
Event: 98.883 Loading class javax/management/ReflectionException done
Event: 98.883 Loading class javax/management/InstanceNotFoundException
Event: 98.884 Loading class javax/management/InstanceNotFoundException done
Event: 99.413 Loading class jdk/internal/reflect/UnsafeQualifiedBooleanFieldAccessorImpl
Event: 99.414 Loading class jdk/internal/reflect/UnsafeQualifiedBooleanFieldAccessorImpl done
Event: 99.699 Loading class java/util/concurrent/ForkJoinTask$AdaptedRunnableAction
Event: 99.700 Loading class java/util/concurrent/ForkJoinTask$AdaptedRunnableAction done
Event: 99.701 Loading class java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory$1
Event: 99.702 Loading class java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory$1 done
Event: 99.771 Loading class java/util/AbstractList$RandomAccessSubList
Event: 99.773 Loading class java/util/AbstractList$RandomAccessSubList done
Event: 99.953 Loading class java/nio/HeapShortBuffer
Event: 99.955 Loading class java/nio/HeapShortBuffer done
Event: 100.814 Loading class java/util/IdentityHashMap$EntrySet
Event: 100.816 Loading class java/util/IdentityHashMap$EntrySet done
Event: 100.825 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 100.827 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 98.078 Thread 0x0000016741ca8280 Exception <a 'sun/nio/fs/WindowsException'{0x00000006105e6ca0}> (0x00000006105e6ca0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 98.527 Thread 0x0000016741cadda0 Exception <a 'sun/nio/fs/WindowsException'{0x000000061e98f6e8}> (0x000000061e98f6e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 98.617 Thread 0x0000016741d27810 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ec44fc8}: static Lcom/android/build/gradle/internal/tasks/MergeNativeLibsTask$InputFile;.<clinit>()V> (0x000000061ec44fc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 99.309 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061f3cfe30}> (0x000000061f3cfe30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.310 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061f3d0ca8}> (0x000000061f3d0ca8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.310 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061f3d0d38}> (0x000000061f3d0d38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.310 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061f3d10b0}> (0x000000061f3d10b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.311 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061f3d2e50}> (0x000000061f3d2e50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.380 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061fb63da8}> (0x000000061fb63da8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.381 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061fb64c40}> (0x000000061fb64c40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.381 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061fb64cd0}> (0x000000061fb64cd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.381 Thread 0x0000016741d27810 Exception <a 'sun/nio/fs/WindowsException'{0x000000061fb650a8}> (0x000000061fb650a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.525 Thread 0x00000167437d69d0 Exception <a 'sun/nio/fs/WindowsException'{0x000000061e6c3440}> (0x000000061e6c3440) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.534 Thread 0x00000167437d69d0 Exception <a 'sun/nio/fs/WindowsException'{0x000000061e7f9ae0}> (0x000000061e7f9ae0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 99.712 Thread 0x00000167437d7900 Implicit null exception at 0x0000016768065cd7 to 0x0000016768066704
Event: 99.776 Thread 0x0000016744f33c40 Exception <a 'java/lang/NoSuchMethodError'{0x000000061f548868}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061f548868) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 101.236 Thread 0x0000016741cadda0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000061dff14e8}: Found class java.lang.Object, but interface was expected> (0x000000061dff14e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 101.493 Thread 0x0000016741cadda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061e0c32c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000061e0c32c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 101.494 Thread 0x0000016741cadda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061e0c7328}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000061e0c7328) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 101.991 Thread 0x0000016741cadda0 Implicit null exception at 0x0000016768309ccf to 0x0000016768309de8

VM Operations (20 events):
Event: 101.219 Executing VM operation: HandshakeAllThreads
Event: 101.219 Executing VM operation: HandshakeAllThreads done
Event: 101.247 Executing VM operation: G1CollectForAllocation
Event: 101.291 Executing VM operation: G1CollectForAllocation done
Event: 101.766 Executing VM operation: ICBufferFull
Event: 101.767 Executing VM operation: ICBufferFull done
Event: 101.986 Executing VM operation: HandshakeAllThreads
Event: 101.986 Executing VM operation: HandshakeAllThreads done
Event: 102.413 Executing VM operation: HandshakeAllThreads
Event: 102.414 Executing VM operation: HandshakeAllThreads done
Event: 102.679 Executing VM operation: ICBufferFull
Event: 102.680 Executing VM operation: ICBufferFull done
Event: 102.680 Executing VM operation: ICBufferFull
Event: 102.681 Executing VM operation: ICBufferFull done
Event: 102.681 Executing VM operation: ICBufferFull
Event: 102.681 Executing VM operation: ICBufferFull done
Event: 102.681 Executing VM operation: ICBufferFull
Event: 102.681 Executing VM operation: ICBufferFull done
Event: 102.976 Executing VM operation: HandshakeAllThreads
Event: 102.977 Executing VM operation: HandshakeAllThreads done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 95.127 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b7fd90
Event: 95.127 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b87290
Event: 95.128 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761bcbf10
Event: 101.175 Thread 0x000001677d2de280 flushing  nmethod 0x0000016768430d10
Event: 101.228 Thread 0x000001677d2de280 flushing  nmethod 0x000001676070ef90
Event: 101.308 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761083190
Event: 101.324 Thread 0x000001677d2de280 flushing  nmethod 0x00000167613cd690
Event: 101.360 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b50a90
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b56d90
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b67490
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b7ae90
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b7cc10
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761b7e210
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761ba9e90
Event: 101.361 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761bb4c10
Event: 101.362 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761bb6590
Event: 101.362 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761bb8290
Event: 101.362 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761bc8910
Event: 101.362 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761be7510
Event: 101.362 Thread 0x000001677d2de280 flushing  nmethod 0x0000016761beb790

Events (20 events):
Event: 99.706 Thread 0x00000167437d7e10 Thread added: 0x00000167437d7900
Event: 99.708 Thread 0x00000167437d7900 Thread added: 0x00000167437d8320
Event: 99.710 Thread 0x00000167437d8320 Thread added: 0x00000167455d03a0
Event: 99.712 Thread 0x00000167455d03a0 Thread added: 0x0000016744f369d0
Event: 99.715 Thread 0x0000016744f369d0 Thread added: 0x0000016744f33730
Event: 99.717 Thread 0x0000016744f33730 Thread added: 0x0000016744f35fb0
Event: 99.719 Thread 0x0000016744f35fb0 Thread added: 0x0000016744f33c40
Event: 99.722 Thread 0x0000016744f33c40 Thread added: 0x0000016744f33220
Event: 99.725 Thread 0x0000016744f33220 Thread added: 0x0000016744f364c0
Event: 99.728 Thread 0x0000016744f364c0 Thread added: 0x0000016744f36ee0
Event: 99.732 Thread 0x0000016744f36ee0 Thread added: 0x0000016744f373f0
Event: 99.736 Thread 0x0000016744f373f0 Thread added: 0x0000016744f37900
Event: 99.740 Thread 0x0000016744f37900 Thread added: 0x0000016744f35590
Event: 99.744 Thread 0x0000016744f35590 Thread added: 0x0000016744f34150
Event: 99.748 Thread 0x0000016744f34150 Thread added: 0x0000016744f37e10
Event: 99.752 Thread 0x0000016744f37e10 Thread added: 0x0000016744f34660
Event: 99.756 Thread 0x0000016744f34660 Thread added: 0x0000016744f38320
Event: 99.760 Thread 0x0000016744f38320 Thread added: 0x0000016744f34b70
Event: 99.764 Thread 0x0000016744f34b70 Thread added: 0x0000016744f35080
Event: 101.538 Thread 0x00000167437d69d0 Thread exited: 0x00000167437d69d0


Dynamic libraries:
0x00007ff7e8420000 - 0x00007ff7e842e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.exe
0x00007ffd53be0000 - 0x00007ffd53e47000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd533c0000 - 0x00007ffd53489000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd51380000 - 0x00007ffd51770000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd51780000 - 0x00007ffd518cb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffcf1110000 - 0x00007ffcf1127000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jli.dll
0x00007ffcf10f0000 - 0x00007ffcf110b000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\VCRUNTIME140.dll
0x00007ffd53570000 - 0x00007ffd53735000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd51350000 - 0x00007ffd51377000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd35780000 - 0x00007ffd35a1a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c\COMCTL32.dll
0x00007ffd53740000 - 0x00007ffd5376b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd50f20000 - 0x00007ffd51058000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd51f20000 - 0x00007ffd51fc9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd511e0000 - 0x00007ffd51283000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd52910000 - 0x00007ffd5293f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd0f1b0000 - 0x00007ffd0f1bc000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\vcruntime140_1.dll
0x00007ffcf1060000 - 0x00007ffcf10ed000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\msvcp140.dll
0x00007ffcf03f0000 - 0x00007ffcf105c000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\server\jvm.dll
0x00007ffd53ae0000 - 0x00007ffd53b94000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd53a30000 - 0x00007ffd53ad6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd53820000 - 0x00007ffd53938000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd537a0000 - 0x00007ffd53814000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd46dc0000 - 0x00007ffd46df5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd50b70000 - 0x00007ffd50bce000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd3ede0000 - 0x00007ffd3edeb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd50b50000 - 0x00007ffd50b64000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd4fa50000 - 0x00007ffd4fa6b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd0bdc0000 - 0x00007ffd0bdca000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jimage.dll
0x00007ffd4e220000 - 0x00007ffd4e461000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd53030000 - 0x00007ffd533b5000 	C:\WINDOWS\System32\combase.dll
0x00007ffd52950000 - 0x00007ffd52a30000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd30870000 - 0x00007ffd308b3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd518d0000 - 0x00007ffd51969000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd04b70000 - 0x00007ffd04b7e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\instrument.dll
0x00007ffcf03c0000 - 0x00007ffcf03e5000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.dll
0x00007ffcf02e0000 - 0x00007ffcf03b7000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jsvml.dll
0x00007ffd51fd0000 - 0x00007ffd5271d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd50d10000 - 0x00007ffd50e83000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffd4e8b0000 - 0x00007ffd4f110000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd52f10000 - 0x00007ffd53005000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd53940000 - 0x00007ffd539aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd50c30000 - 0x00007ffd50c59000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffcf0280000 - 0x00007ffcf0298000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
0x00007ffcf02c0000 - 0x00007ffcf02d9000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\net.dll
0x00007ffd492f0000 - 0x00007ffd4940e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd4ffc0000 - 0x00007ffd5002b000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcf02a0000 - 0x00007ffcf02b6000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\nio.dll
0x00007ffd08710000 - 0x00007ffd08720000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\verify.dll
0x00007ffd3e0a0000 - 0x00007ffd3e0c7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffd044a0000 - 0x00007ffd04518000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffd08540000 - 0x00007ffd08549000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management.dll
0x00007ffd058e0000 - 0x00007ffd058eb000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management_ext.dll
0x00007ffd52ce0000 - 0x00007ffd52ce8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd4f4f0000 - 0x00007ffd4f523000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd52ea0000 - 0x00007ffd52eaa000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd4a550000 - 0x00007ffd4a56f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd4a520000 - 0x00007ffd4a545000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffd4f530000 - 0x00007ffd4f656000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd04cd0000 - 0x00007ffd04cd9000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\extnet.dll
0x00007ffd503a0000 - 0x00007ffd503bb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd4f9b0000 - 0x00007ffd4f9eb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd50060000 - 0x00007ffd5008b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd50c00000 - 0x00007ffd50c26000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffd50200000 - 0x00007ffd5020c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd21300000 - 0x00007ffd2130e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\sunmscapi.dll
0x00007ffd51060000 - 0x00007ffd511d7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffd50500000 - 0x00007ffd50530000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffd504b0000 - 0x00007ffd504ef000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffd36ae0000 - 0x00007ffd36ae8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffd32d30000 - 0x00007ffd32d63000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\1_SangforNspX64.dll
0x00007ffd52cf0000 - 0x00007ffd52e90000 	C:\WINDOWS\System32\ole32.dll
0x00007ffd05260000 - 0x00007ffd05278000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffd05240000 - 0x00007ffd05252000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffd05200000 - 0x00007ffd05230000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffd46510000 - 0x00007ffd46530000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffd48c50000 - 0x00007ffd48c5b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd4a800000 - 0x00007ffd4a886000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffd4fb80000 - 0x00007ffd4fbb6000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.13\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c;C:\Users\<USER>\.jdks\corretto-17.0.13\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu;C:\Program Files (x86)\Sangfor\SSL\ClientComponent

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC -XX:G1HeapRegionSize=32m -XX:+UseStringDeduplication --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.desktop/java.awt.font=ALL-UNNAMED -Xmx8192m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\gradle-daemon-main-8.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 33554432                                  {product} {command line}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8589934592                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 5133828096                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 33554432                                  {product} {ergonomic}
   size_t MinHeapSize                              = 33554432                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8589934592                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {command line}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseStringDeduplication                   = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\corretto-17.0.13
CLASSPATH=E:\StudioProjects\AIOSService\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\PowerShell\7;C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Program Files\TortoiseSVN\bin;E:\Sdk\platform-tools;C:\Program Files\CorpLink\current\module\mdm\x64\policy\bin;C:\Program Files\dotnet\;D:\Program Files\Git\cmd;E:\apktool;C:\Users\<USER>\.jdks\corretto-17.0.13\bin;E:\apktool\dextools;D:\scrcpy;E:\Sdk\tools;E:\Sdk\build-tools;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Muse Hub\lib;C:\Program Files\PowerShell\7\;D:\Program Files\nodejs\;D:\program files\nodejs\;C:\Users\<USER>\AppData\Local\cloudbase-cli\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Users\dongjs\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin;C:\Users\<USER>\AppData\Local\Programs\EmEditor;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Users\dongjs\AppData\Local\Programs\CodeBuddy\bin;D:\program files\nodejs
USERNAME=dongjs
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 186 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
OS uptime: 8 days 3:33 hours
Hyper-V role detected

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 186 stepping 2 microcode 0x4114, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 1
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 2
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 3
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 4
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 5
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 6
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 7
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 8
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 9
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 10
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 11
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 12
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 13
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 14
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 15
  Max Mhz: 2400, Current Mhz: 1200, Mhz Limit: 2400
Processor Information for processor 16
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 17
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 18
  Max Mhz: 2400, Current Mhz: 1200, Mhz Limit: 2400
Processor Information for processor 19
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400

Memory: 4k page, system-wide physical 32492M (3636M free)
TotalPageFile size 55092M (AvailPageFile size 42M)
current process WorkingSet (physical memory assigned to process): 915M, peak: 923M
current process commit charge ("private bytes"): 1074M, peak: 1083M

vm_info: OpenJDK 64-Bit Server VM (17.0.13+11-LTS) for windows-amd64 JRE (17.0.13+11-LTS), built on Oct 10 2024 19:51:30 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
